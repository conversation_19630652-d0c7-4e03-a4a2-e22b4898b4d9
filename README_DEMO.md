# 🎤 Slovenský hlasový chat - DEMO

## 🚀 Aplikácia je SPUSTENÁ!

V<PERSON><PERSON> slovenská chatová aplikácia je úspešne spustená a dostupná na:

**🌐 http://localhost:8000**

## 📋 Čo vidíte v prehliadači:

### ✅ **Funkčná webová aplikácia s:**
- 💬 **Interaktívny chat** s moderným dizajnom
- 🤖 **Demo chatbot** s slovenskými odpoveďami
- 📱 **Responzívne rozhranie** pre mobily a desktop
- 🎨 **Krásny gradient dizajn** s animáciami

### ✅ **Testovateľné funkcie:**
- Napíšte **"ahoj"** - dostanete slovenské privítanie
- Napíšte **"test"** - overíte funkčnosť
- Napíšte **"slovenčina"** - informácie o jazyku
- Napíšte **"čas"** - aktuálny čas
- Napíšte **"pomoc"** - zoznam príkazov

## 🔧 Aktuálny stav:

### ✅ **Čo funguje:**
- ✅ Webový server na porte 8000
- ✅ HTML/CSS/JavaScript frontend
- ✅ Demo chatbot s slovenskými odpoveďami
- ✅ Responzívny dizajn
- ✅ Interaktívne ovládanie

### 🔄 **Čo je pripravené na rozšírenie:**
- 🔄 OpenAI API integrácia (v `voice_chat_app/` priečinku)
- 🔄 Piper TTS pre slovenský hlas
- 🔄 Rozpoznávanie reči
- 🔄 Cloudflare nasadenie

## 🎯 Ako testovať aplikáciu:

1. **Otvorte prehliadač** na http://localhost:8000
2. **Napíšte správu** do textového poľa
3. **Stlačte Enter** alebo kliknite "Odoslať"
4. **Sledujte odpoveď** chatbota

### 🧪 **Testové príkazy:**
```
ahoj
ako sa máš
test
slovenčina
čas
dátum
pomoc
ďakujem
```

## 📊 **Technické detaily:**

### **Frontend:**
- HTML5 s moderným CSS3 dizajnom
- Vanilla JavaScript (bez externých knižníc)
- Responzívny layout s Flexbox
- Gradient pozadie a animácie

### **Backend:**
- Python HTTP server na porte 8000
- Statické súbory servované z aktuálneho priečinka
- Demo chatbot logika v JavaScript

### **Súbory:**
- `index.html` - Hlavná aplikácia
- `voice_chat_app/` - Kompletná Flask aplikácia s OpenAI
- `simple_chat.py` - Standalone Python server

## 🚀 **Ďalšie kroky:**

### 1. **Pre plnú funkcionalitu:**
```bash
cd voice_chat_app
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python app.py
```

### 2. **Pre nasadenie na Cloudflare:**
- Nahrajte `voice_chat_app/` na GitHub
- Pripojte k Cloudflare Pages
- Nastavte environment premenné

### 3. **Pre Docker:**
```bash
cd voice_chat_app
docker build -t slovak-chat .
docker run -p 5000:5000 slovak-chat
```

## 🎉 **Gratulujeme!**

Úspešne ste spustili slovenskú chatovú aplikáciu! 

- ✅ **Demo verzia** funguje lokálne
- ✅ **Plná verzia** je pripravená v `voice_chat_app/`
- ✅ **Nasadenie** je pripravené pre Cloudflare

**Aplikácia je pripravená na použitie a ďalší vývoj!** 🚀

---

**Server beží na pozadí. Pre ukončenie stlačte Ctrl+C v termináli.**
