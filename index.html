<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Slovenský hlasový chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .chat-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s;
        }

        .message-input:focus {
            border-color: #007bff;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .status {
            text-align: center;
            padding: 10px;
            font-size: 12px;
            color: #6c757d;
        }

        .demo-info {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            margin: 20px;
            border-radius: 10px;
            border: 1px solid #ffeaa7;
            text-align: center;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            justify-content: center;
        }

        @media (max-width: 600px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }
            
            .input-group {
                flex-wrap: wrap;
            }
            
            .message-input {
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🎤 Slovenský hlasový chat
            <div style="font-size: 14px; font-weight: normal; margin-top: 5px;">
                Demo verzia - lokálne spustené
            </div>
        </div>
        
        <div class="demo-info">
            <strong>📋 Demo režim:</strong> Toto je zjednodušená verzia bez OpenAI API. 
            Chatbot odpovedá na základe jednoduchých pravidiel.
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    Ahoj! Som váš slovenský demo asistent. Skúste napísať "ahoj", "ako sa máš", "test" alebo "slovenčina". 
                    Ako vám môžem pomôcť?
                </div>
            </div>
        </div>
        
        <div class="status" id="status">Pripravený na komunikáciu</div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="message-input" id="messageInput" 
                       placeholder="Napíšte správu..." 
                       onkeypress="handleKeyPress(event)">
                <button class="btn btn-primary" onclick="sendMessage()">Odoslať</button>
            </div>
            <div class="controls">
                <button class="btn btn-secondary" onclick="clearChat()">Vymazať chat</button>
                <button class="btn btn-secondary" onclick="showInfo()">Info</button>
            </div>
        </div>
    </div>

    <script>
        // Simulácia chatbota bez servera
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Pridáme správu používateľa
            addMessage('user', message);
            input.value = '';
            
            // Zobrazíme loading
            setStatus('Spracovávam odpoveď...');
            
            // Simulácia oneskorenia
            setTimeout(() => {
                const response = getResponse(message);
                addMessage('assistant', response);
                setStatus('Pripravený na komunikáciu');
            }, 500);
        }

        // Jednoduchý chatbot
        function getResponse(message) {
            const msg = message.toLowerCase();
            
            if (msg.includes('ahoj') || msg.includes('dobrý')) {
                return 'Ahoj! Teší ma, že sa so mnou rozprávate. Ako sa máte?';
            } else if (msg.includes('ako sa máš')) {
                return 'Ďakujem za otázku! Som demo AI asistent a funguje mi to výborne. Ako vám môžem pomôcť?';
            } else if (msg.includes('test')) {
                return 'Test prebehol úspešne! ✅ Demo aplikácia funguje správne. Môžete pokračovať v konverzácii.';
            } else if (msg.includes('slovenčina') || msg.includes('slovenský')) {
                return 'Áno, hovorím po slovensky! 🇸🇰 Slovenčina je krásny jazyk s bohatou históriou a kultúrou.';
            } else if (msg.includes('ďakujem') || msg.includes('vďaka')) {
                return 'Nemáte za čo! Som tu, aby som vám pomohol. Máte ešte nejaké otázky?';
            } else if (msg.includes('počasie')) {
                return 'Bohužiaľ, v demo režime nemám prístup k aktuálnym informáciám o počasí. 🌤️';
            } else if (msg.includes('čas')) {
                return `Aktuálny čas je: ${new Date().toLocaleTimeString('sk-SK')} ⏰`;
            } else if (msg.includes('dátum')) {
                return `Dnešný dátum je: ${new Date().toLocaleDateString('sk-SK')} 📅`;
            } else if (msg.includes('pomoc') || msg.includes('help')) {
                return 'Môžem odpovedať na jednoduché otázky. Skúste: "ahoj", "ako sa máš", "test", "slovenčina", "čas", "dátum".';
            } else {
                return `Rozumiem vašej správe: "${message}". Som demo chatbot a snažím sa vám pomôcť ako najlepšie viem. Pre viac funkcií napíšte "pomoc". 🤖`;
            }
        }

        // Pridanie správy do chatu
        function addMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Pomocné funkcie
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function setStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function clearChat() {
            document.getElementById('chatMessages').innerHTML = `
                <div class="message assistant">
                    <div class="message-content">
                        Chat bol vymazaný. Ako vám môžem pomôcť?
                    </div>
                </div>
            `;
            setStatus('Chat vymazaný');
        }

        function showInfo() {
            addMessage('assistant', 
                '📋 Informácie o aplikácii:\n\n' +
                '🎯 Toto je demo verzia slovenského hlasového chatu\n' +
                '🤖 Chatbot funguje na základe jednoduchých pravidiel\n' +
                '🚀 Plná verzia používa OpenAI API a Piper TTS\n' +
                '💻 Aplikácia je pripravená na nasadenie na Cloudflare\n\n' +
                'Skúste napísať: "test", "slovenčina", "čas", "pomoc"'
            );
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('messageInput').focus();
            
            // Uvítacia správa po 2 sekundách
            setTimeout(() => {
                addMessage('assistant', 
                    '👋 Vitajte v demo verzii slovenského chatu! Skúste napísať "test" alebo "pomoc" pre začiatok.'
                );
            }, 2000);
        });
    </script>
</body>
</html>
