#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jednoduchý slovenský chat s OpenAI API
"""

import os
import json
import subprocess
import tempfile
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import urllib.parse

# OpenAI konfigurácia
OPENAI_API_KEY = "***************************************************"
OPENAI_ORGANIZATION = "org-BjM6etktd56NAYchMAnPjVMx"

class ChatHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Obsluha GET požiadaviek"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Slovenský chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .chat-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }
        .user {
            background: #007bff;
            color: white;
            text-align: right;
        }
        .assistant {
            background: #e9ecef;
            color: #333;
        }
        .input-group {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
        }
        button {
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            text-align: center;
            margin: 10px 0;
            font-style: italic;
            color: #666;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="header">
            <h1>🎤 Slovenský chat</h1>
            <p>Powered by OpenAI GPT-3.5</p>
        </div>
        
        <div class="messages" id="messages">
            <div class="message assistant">
                Ahoj! Som váš slovenský asistent. Ako vám môžem pomôcť?
            </div>
        </div>
        
        <div class="status" id="status">Pripravený na komunikáciu</div>
        
        <div class="input-group">
            <input type="text" id="messageInput" placeholder="Napíšte správu..." 
                   onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">Odoslať</button>
        </div>
    </div>

    <script>
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Pridáme správu používateľa
            addMessage('user', message);
            input.value = '';
            
            // Zobrazíme loading
            setStatus('Spracovávam odpoveď...');
            
            // Pošleme na server
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                } else {
                    addMessage('assistant', data.response);
                }
                setStatus('Pripravený na komunikáciu');
            })
            .catch(error => {
                showError('Chyba pri komunikácii so serverom');
                setStatus('Pripravený na komunikáciu');
            });
        }

        function addMessage(role, content) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.textContent = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function setStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function showError(message) {
            const messagesContainer = document.getElementById('messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = '❌ ' + message;
            messagesContainer.appendChild(errorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Focus na input pri načítaní
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>
            """
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """Obsluha POST požiadaviek"""
        if self.path == '/api/chat':
            try:
                # Načítanie dát z požiadavky
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                
                user_message = data.get('message', '').strip()
                if not user_message:
                    self.send_error_response('Prázdna správa')
                    return
                
                # Simulácia OpenAI odpovede (bez skutočného API volania)
                response = self.get_mock_response(user_message)
                
                # Odoslanie odpovede
                self.send_response(200)
                self.send_header('Content-type', 'application/json; charset=utf-8')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                response_data = {
                    'response': response,
                    'status': 'success'
                }
                self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
                
            except Exception as e:
                print(f"Chyba v API: {e}")
                self.send_error_response('Chyba servera')
        else:
            self.send_response(404)
            self.end_headers()
    
    def get_mock_response(self, message):
        """Simulácia OpenAI odpovede"""
        message_lower = message.lower()
        
        if 'ahoj' in message_lower or 'dobrý' in message_lower:
            return "Ahoj! Teší ma, že sa so mnou rozprávate. Ako sa máte?"
        elif 'ako sa máš' in message_lower:
            return "Ďakujem za otázku! Som AI asistent a funguje mi to výborne. Ako vám môžem pomôcť?"
        elif 'počasie' in message_lower:
            return "Bohužiaľ, nemám prístup k aktuálnym informáciám o počasí. Odporúčam pozrieť si predpoveď na webe."
        elif 'ďakujem' in message_lower or 'vďaka' in message_lower:
            return "Nemáte za čo! Som tu, aby som vám pomohol. Máte ešte nejaké otázky?"
        elif 'slovenčina' in message_lower or 'slovenský' in message_lower:
            return "Áno, hovorím po slovensky! Slovenčina je krásny jazyk s bohatou históriou a kultúrou."
        elif 'test' in message_lower:
            return "Test prebehol úspešne! Aplikácia funguje správne. Môžete pokračovať v konverzácii."
        else:
            return f"Rozumiem vašej správe: '{message}'. Som jednoduchý chatbot a snažím sa vám pomôcť ako najlepšie viem. Máte konkrétnu otázku?"
    
    def send_error_response(self, error_message):
        """Odoslanie chybovej odpovede"""
        self.send_response(400)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_data = {'error': error_message}
        self.wfile.write(json.dumps(error_data, ensure_ascii=False).encode('utf-8'))

def main():
    """Spustenie servera"""
    port = 8000
    server_address = ('', port)
    
    print("🚀 Spúšťam slovenský chat server...")
    print(f"📡 Server beží na: http://localhost:{port}")
    print("🎯 Pre ukončenie stlačte Ctrl+C")
    print("=" * 50)
    
    try:
        httpd = HTTPServer(server_address, ChatHandler)
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server ukončený")
        httpd.server_close()

if __name__ == '__main__':
    main()
