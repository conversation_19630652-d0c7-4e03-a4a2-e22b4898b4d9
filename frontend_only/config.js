// Konfigur<PERSON>cia pre slovenský hlasový chat frontend
const CONFIG = {
    // Predvolené URL backend servera
    DEFAULT_SERVER_URL: 'http://localhost:5000',
    
    // Oracle server URL (upravte podľa vašej konfigur<PERSON>)
    ORACLE_SERVER_URL: 'https://your-oracle-server.com/api',
    
    // API endpoints
    ENDPOINTS: {
        HEALTH: '/health',
        CHAT: '/api/chat',
        SPEAK: '/api/speak',
        HISTORY: '/api/history',
        CLEAR: '/api/clear'
    },
    
    // Timeout pre API volania (v milisekundách)
    API_TIMEOUT: 30000,
    
    // Nastavenia pre rozpoznávanie reči
    SPEECH_RECOGNITION: {
        LANGUAGE: 'sk-SK',
        CONTINUOUS: false,
        INTERIM_RESULTS: false
    },
    
    // Nastavenia pre text-to-speech
    TEXT_TO_SPEECH: {
        ENABLED: true,
        AUTO_PLAY: false
    },
    
    // UI nastavenia
    UI: {
        MAX_MESSAGE_LENGTH: 1000,
        TYPING_INDICATOR_DELAY: 500,
        AUTO_SCROLL: true,
        SAVE_HISTORY: true
    },
    
    // Lokalizácia
    MESSAGES: {
        CONNECTING: 'Pripájam sa na server...',
        CONNECTED: 'Pripojené ✓',
        DISCONNECTED: 'Nepripojené',
        CONNECTION_ERROR: 'Chyba pripojenia ✗',
        PROCESSING: 'Spracovávam odpoveď...',
        READY: 'Pripravený na komunikáciu',
        GENERATING_SPEECH: 'Generujem hlas...',
        PLAYING_SPEECH: 'Prehrávam hlas...',
        RECORDING: 'Nahrávam...',
        PROCESSING_RECORDING: 'Spracovávam nahrávku...',
        CHAT_CLEARED: 'Chat vymazaný',
        HISTORY_LOADED: 'História načítaná',
        SETUP_SERVER: 'Nastavte backend server',
        WELCOME: 'Nastavte URL vašeho Oracle backend servera a otestujte pripojenie.',
        CONNECTION_SUCCESS: 'Úspešne pripojené na backend server! Môžete začať chatovať.',
        SPEECH_NOT_IMPLEMENTED: 'Rozpoznávanie reči nie je zatiaľ implementované. Použite textový vstup.'
    },
    
    // Predvolené správy
    DEFAULT_MESSAGES: [
        {
            role: 'system',
            content: 'Nastavte URL vašeho Oracle backend servera a otestujte pripojenie.'
        }
    ]
};

// Export pre použitie v hlavnom skripte
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
