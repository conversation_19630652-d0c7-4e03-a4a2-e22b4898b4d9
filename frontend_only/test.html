<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test API pripojenia</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test API pripojenia</h1>
            <p>Nástroj na testovanie komunikácie s Oracle backend serverom</p>
        </div>

        <div class="test-section">
            <h3>🔧 Konfigurácia servera</h3>
            <input type="text" id="serverUrl" placeholder="https://your-oracle-server.com/api" value="http://localhost:5000">
            <button onclick="saveConfig()">Uložiť konfiguráciu</button>
        </div>

        <div class="test-section">
            <h3>🏥 Test zdravia servera</h3>
            <p>Overí, či server beží a odpovedá na základné požiadavky.</p>
            <button onclick="testHealth()">Test /health endpoint</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💬 Test chat API</h3>
            <p>Otestuje odoslanie správy a získanie odpovede.</p>
            <input type="text" id="testMessage" placeholder="Testovacia správa" value="Ahoj, toto je test">
            <button onclick="testChat()">Test /api/chat endpoint</button>
            <div id="chatResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔊 Test TTS API</h3>
            <p>Otestuje generovanie hlasu z textu.</p>
            <input type="text" id="ttsText" placeholder="Text na konverziu" value="Toto je test slovenského hlasu">
            <button onclick="testTTS()">Test /api/speak endpoint</button>
            <div id="ttsResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📚 Test histórie</h3>
            <p>Otestuje načítanie a vymazanie histórie konverzácie.</p>
            <button onclick="testHistory()">Test /api/history endpoint</button>
            <button onclick="testClear()">Test /api/clear endpoint</button>
            <div id="historyResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 Kompletný test</h3>
            <p>Spustí všetky testy postupne.</p>
            <button onclick="runAllTests()">Spustiť všetky testy</button>
            <div id="allTestsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let serverUrl = '';

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            const savedUrl = localStorage.getItem('testServerUrl');
            if (savedUrl) {
                document.getElementById('serverUrl').value = savedUrl;
                serverUrl = savedUrl;
            }
        });

        // Uloženie konfigurácie
        function saveConfig() {
            const url = document.getElementById('serverUrl').value.trim();
            if (url) {
                serverUrl = url;
                localStorage.setItem('testServerUrl', url);
                showResult('allTestsResult', 'info', `Konfigurácia uložená: ${url}`);
            }
        }

        // Zobrazenie výsledku
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // Test zdravia servera
        async function testHealth() {
            if (!serverUrl) {
                showResult('healthResult', 'error', 'Najprv nastavte URL servera');
                return;
            }

            showResult('healthResult', 'loading', 'Testujem pripojenie...');

            try {
                const startTime = Date.now();
                const response = await fetch(`${serverUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (response.ok) {
                    const data = await response.text();
                    showResult('healthResult', 'success', 
                        `✅ Server je dostupný!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Čas odpovede: ${responseTime}ms\n` +
                        `Odpoveď: ${data}`
                    );
                } else {
                    showResult('healthResult', 'error', 
                        `❌ Server odpovedal s chybou!\n` +
                        `Status: ${response.status} ${response.statusText}`
                    );
                }
            } catch (error) {
                showResult('healthResult', 'error', 
                    `❌ Chyba pripojenia!\n` +
                    `Chyba: ${error.message}\n` +
                    `Tip: Skontrolujte URL servera a CORS nastavenia`
                );
            }
        }

        // Test chat API
        async function testChat() {
            if (!serverUrl) {
                showResult('chatResult', 'error', 'Najprv nastavte URL servera');
                return;
            }

            const message = document.getElementById('testMessage').value.trim();
            if (!message) {
                showResult('chatResult', 'error', 'Zadajte testovaciu správu');
                return;
            }

            showResult('chatResult', 'loading', 'Testujem chat API...');

            try {
                const startTime = Date.now();
                const response = await fetch(`${serverUrl}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (response.ok) {
                    const data = await response.json();
                    showResult('chatResult', 'success', 
                        `✅ Chat API funguje!\n` +
                        `Čas odpovede: ${responseTime}ms\n` +
                        `Odoslaná správa: "${message}"\n` +
                        `Odpoveď: "${data.response || 'Žiadna odpoveď'}"\n` +
                        `Status: ${data.status || 'neznámy'}`
                    );
                } else {
                    const errorText = await response.text();
                    showResult('chatResult', 'error', 
                        `❌ Chat API chyba!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Odpoveď: ${errorText}`
                    );
                }
            } catch (error) {
                showResult('chatResult', 'error', 
                    `❌ Chyba chat API!\n` +
                    `Chyba: ${error.message}`
                );
            }
        }

        // Test TTS API
        async function testTTS() {
            if (!serverUrl) {
                showResult('ttsResult', 'error', 'Najprv nastavte URL servera');
                return;
            }

            const text = document.getElementById('ttsText').value.trim();
            if (!text) {
                showResult('ttsResult', 'error', 'Zadajte text na konverziu');
                return;
            }

            showResult('ttsResult', 'loading', 'Testujem TTS API...');

            try {
                const startTime = Date.now();
                const response = await fetch(`${serverUrl}/api/speak`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                });
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (response.ok) {
                    const blob = await response.blob();
                    const audioSize = blob.size;
                    
                    showResult('ttsResult', 'success', 
                        `✅ TTS API funguje!\n` +
                        `Čas odpovede: ${responseTime}ms\n` +
                        `Text: "${text}"\n` +
                        `Audio veľkosť: ${audioSize} bajtov\n` +
                        `MIME typ: ${blob.type}`
                    );

                    // Pokus o prehranie audio
                    try {
                        const audio = new Audio();
                        audio.src = URL.createObjectURL(blob);
                        audio.play();
                    } catch (audioError) {
                        console.warn('Chyba pri prehrávaní audio:', audioError);
                    }
                } else {
                    const errorText = await response.text();
                    showResult('ttsResult', 'error', 
                        `❌ TTS API chyba!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Odpoveď: ${errorText}`
                    );
                }
            } catch (error) {
                showResult('ttsResult', 'error', 
                    `❌ Chyba TTS API!\n` +
                    `Chyba: ${error.message}`
                );
            }
        }

        // Test histórie
        async function testHistory() {
            if (!serverUrl) {
                showResult('historyResult', 'error', 'Najprv nastavte URL servera');
                return;
            }

            showResult('historyResult', 'loading', 'Testujem history API...');

            try {
                const response = await fetch(`${serverUrl}/api/history`);

                if (response.ok) {
                    const data = await response.json();
                    const historyCount = data.history ? data.history.length : 0;
                    
                    showResult('historyResult', 'success', 
                        `✅ History API funguje!\n` +
                        `Počet správ v histórii: ${historyCount}\n` +
                        `Status: ${data.status || 'neznámy'}\n` +
                        `Posledné správy: ${JSON.stringify(data.history?.slice(-3) || [], null, 2)}`
                    );
                } else {
                    const errorText = await response.text();
                    showResult('historyResult', 'error', 
                        `❌ History API chyba!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Odpoveď: ${errorText}`
                    );
                }
            } catch (error) {
                showResult('historyResult', 'error', 
                    `❌ Chyba history API!\n` +
                    `Chyba: ${error.message}`
                );
            }
        }

        // Test vymazania histórie
        async function testClear() {
            if (!serverUrl) {
                showResult('historyResult', 'error', 'Najprv nastavte URL servera');
                return;
            }

            showResult('historyResult', 'loading', 'Testujem clear API...');

            try {
                const response = await fetch(`${serverUrl}/api/clear`);

                if (response.ok) {
                    const data = await response.json();
                    showResult('historyResult', 'success', 
                        `✅ Clear API funguje!\n` +
                        `Status: ${data.status || 'neznámy'}\n` +
                        `Správa: ${data.message || 'História vymazaná'}`
                    );
                } else {
                    const errorText = await response.text();
                    showResult('historyResult', 'error', 
                        `❌ Clear API chyba!\n` +
                        `Status: ${response.status} ${response.statusText}\n` +
                        `Odpoveď: ${errorText}`
                    );
                }
            } catch (error) {
                showResult('historyResult', 'error', 
                    `❌ Chyba clear API!\n` +
                    `Chyba: ${error.message}`
                );
            }
        }

        // Spustenie všetkých testov
        async function runAllTests() {
            if (!serverUrl) {
                showResult('allTestsResult', 'error', 'Najprv nastavte URL servera');
                return;
            }

            showResult('allTestsResult', 'loading', 'Spúšťam všetky testy...\n');

            const tests = [
                { name: 'Health Check', func: testHealth, resultId: 'healthResult' },
                { name: 'Chat API', func: testChat, resultId: 'chatResult' },
                { name: 'TTS API', func: testTTS, resultId: 'ttsResult' },
                { name: 'History API', func: testHistory, resultId: 'historyResult' }
            ];

            let results = [];
            
            for (const test of tests) {
                try {
                    await test.func();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Pauza medzi testami
                    
                    const resultElement = document.getElementById(test.resultId);
                    const success = resultElement.className.includes('success');
                    results.push(`${test.name}: ${success ? '✅ ÚSPECH' : '❌ CHYBA'}`);
                } catch (error) {
                    results.push(`${test.name}: ❌ CHYBA - ${error.message}`);
                }
            }

            const successCount = results.filter(r => r.includes('✅')).length;
            const totalCount = results.length;
            
            showResult('allTestsResult', 
                successCount === totalCount ? 'success' : 'error',
                `Výsledky testov (${successCount}/${totalCount} úspešných):\n\n` +
                results.join('\n') + '\n\n' +
                (successCount === totalCount ? 
                    '🎉 Všetky testy prešli! Backend server je pripravený.' :
                    '⚠️ Niektoré testy zlyhali. Skontrolujte konfiguráciu servera.')
            );
        }
    </script>
</body>
</html>
