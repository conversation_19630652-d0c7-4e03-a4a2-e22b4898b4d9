# 🎤 Slovenský hlasový chat - Frontend Only

<PERSON><PERSON><PERSON> frontend pre slovenský hlasový chat, k<PERSON><PERSON> sa pripája na vzdialený backend server bež<PERSON>i na Oracle.

## 🎯 Účel

Tento frontend je navrhnutý pre scenár, kde:
- **Frontend** be<PERSON><PERSON> (alebo na Cloudflare Pages)
- **Backend** bež<PERSON> na Oracle serveri
- Komunikácia prebieha cez REST API

## 🚀 Rýchle spustenie

### 1. Lokálne spustenie

```bash
# Spustenie jednoduchého HTTP servera
cd frontend_only
python3 -m http.server 8080

# Alebo použite Node.js
npx serve . -p 8080

# Alebo použite PHP
php -S localhost:8080
```

Aplikácia bude dostupná na: **http://localhost:8080**

### 2. Konfigurácia backend servera

1. Otvorte aplikáciu v prehliadači
2. V poli "Backend URL" zadajte URL vašeho Oracle servera:
   ```
   https://your-oracle-server.com/api
   ```
3. Kliknite "Test pripojenia"
4. Po úspešnom pripojení môžete začať chatovať

## 📁 Štruktúra súborov

```
frontend_only/
├── index.html          # Hlavná aplikácia
├── config.js          # Konfiguračné nastavenia
├── README.md          # Dokumentácia
└── assets/            # Statické súbory (voliteľné)
```

## 🔧 Konfigurácia

### Backend API endpoints

Frontend očakáva tieto API endpoints na backend serveri:

```
GET  /health           # Test pripojenia
POST /api/chat         # Odoslanie správy
POST /api/speak        # Text-to-speech
GET  /api/history      # Získanie histórie
GET  /api/clear        # Vymazanie histórie
```

### Upravenie konfigurácie

Editujte `config.js` súbor:

```javascript
const CONFIG = {
    // Predvolené URL vašeho Oracle servera
    DEFAULT_SERVER_URL: 'https://your-oracle-server.com/api',
    
    // Timeout pre API volania
    API_TIMEOUT: 30000,
    
    // Ďalšie nastavenia...
};
```

## 🌐 Nasadenie na Cloudflare Pages

### 1. Príprava

```bash
# Vytvorte Git repozitár
git init
git add .
git commit -m "Frontend for Slovak Voice Chat"

# Nahrajte na GitHub
git remote add origin https://github.com/YOUR_USERNAME/slovak-chat-frontend.git
git push -u origin main
```

### 2. Cloudflare Pages setup

1. Prihlásenie do Cloudflare Dashboard
2. Pages → Create a project → Connect to Git
3. Vyberte váš GitHub repozitár
4. Build settings:
   ```
   Framework preset: None
   Build command: (prázdne)
   Build output directory: /
   Root directory: frontend_only
   ```

### 3. Custom domain (voliteľné)

```
# Pridanie vlastnej domény
slovak-chat.yourdomain.com
```

## 🔒 CORS konfigurácia

Váš Oracle backend server musí povoliť CORS pre frontend doménu:

```python
# Flask príklad
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=[
    "http://localhost:8080",
    "https://your-frontend-domain.com"
])
```

```javascript
// Express.js príklad
app.use(cors({
    origin: [
        'http://localhost:8080',
        'https://your-frontend-domain.com'
    ]
}));
```

## 📡 API komunikácia

### Request formáty

**Chat správa:**
```json
POST /api/chat
{
    "message": "Ahoj, ako sa máš?"
}
```

**Text-to-speech:**
```json
POST /api/speak
{
    "text": "Text na konverziu na hlas"
}
```

### Response formáty

**Chat odpoveď:**
```json
{
    "response": "Ahoj! Mám sa dobre, ďakujem.",
    "status": "success"
}
```

**História:**
```json
{
    "history": [
        {"role": "user", "content": "Ahoj"},
        {"role": "assistant", "content": "Ahoj! Ako sa máte?"}
    ],
    "status": "success"
}
```

## 🎨 Funkcie

### ✅ Implementované:
- 💬 **Textový chat** s backend serverom
- 🔊 **Text-to-speech** (ak podporované backendom)
- 📱 **Responzívny dizajn**
- 🔄 **Automatické pripojenie**
- 💾 **Ukladanie URL servera**
- 📊 **Status indikátor pripojenia**
- 🧪 **Test pripojenia**

### 🔄 Pripravené na implementáciu:
- 🎤 **Speech-to-text** (Web Speech API)
- 📝 **Offline režim**
- 🔔 **Notifikácie**
- 🎨 **Témy a personalizácia**

## 🐛 Riešenie problémov

### Backend server nedostupný
```
❌ Chyba: Failed to fetch
```
**Riešenie:**
1. Skontrolujte URL servera
2. Overte, že server beží
3. Skontrolujte CORS nastavenia

### CORS chyby
```
❌ Access to fetch blocked by CORS policy
```
**Riešenie:**
1. Pridajte frontend doménu do CORS whitelist
2. Skontrolujte HTTP vs HTTPS

### Timeout chyby
```
❌ Request timeout
```
**Riešenie:**
1. Zvýšte `API_TIMEOUT` v config.js
2. Optimalizujte backend server

## 🔧 Vývoj

### Lokálne testovanie s mock backendom

```javascript
// Pridajte do index.html pre testovanie
const MOCK_MODE = true;

if (MOCK_MODE) {
    // Simulácia API odpovedí
    window.mockAPI = {
        chat: (message) => `Mock odpoveď na: ${message}`,
        speak: (text) => new Blob(['mock audio'], {type: 'audio/wav'})
    };
}
```

### Debug režim

```javascript
// Zapnutie debug logov
localStorage.setItem('debug', 'true');
```

## 📞 Podpora

Pre otázky a problémy:
1. Skontrolujte browser console (F12)
2. Overte network tab pre API volania
3. Vytvorte issue na GitHub

---

**Frontend pripravený na pripojenie k vašemu Oracle backend serveru!** 🚀
