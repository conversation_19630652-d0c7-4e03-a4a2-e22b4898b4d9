<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Slovenský hlasový chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .chat-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .server-config {
            background: #e3f2fd;
            padding: 15px;
            border-bottom: 1px solid #bbdefb;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .server-input {
            flex: 1;
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid #90caf9;
            border-radius: 20px;
            font-size: 14px;
        }

        .server-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-connected {
            background: #c8e6c9;
            color: #2e7d32;
        }

        .status-disconnected {
            background: #ffcdd2;
            color: #c62828;
        }

        .status-connecting {
            background: #fff3e0;
            color: #ef6c00;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .message.system {
            justify-content: center;
        }

        .message.system .message-content {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-style: italic;
            text-align: center;
        }

        .message-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
        }

        .btn-speak {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-speak:hover {
            background: #218838;
        }

        .btn-speak:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s;
        }

        .message-input:focus {
            border-color: #007bff;
        }

        .message-input:disabled {
            background: #f8f9fa;
            color: #6c757d;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn:disabled {
            background: #6c757d !important;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }

        .btn-record {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-record:hover:not(:disabled) {
            background: #c82333;
        }

        .btn-record.recording {
            background: #28a745;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .status {
            text-align: center;
            padding: 10px;
            font-size: 12px;
            color: #6c757d;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        @media (max-width: 600px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }
            
            .input-group {
                flex-wrap: wrap;
            }
            
            .message-input {
                min-width: 200px;
            }

            .server-config {
                flex-direction: column;
                align-items: stretch;
            }

            .server-input {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🎤 Slovenský hlasový chat
            <div style="font-size: 14px; font-weight: normal; margin-top: 5px;">
                Frontend pripojený na Oracle backend
            </div>
        </div>
        
        <div class="server-config">
            <label for="serverUrl" style="font-weight: bold; color: #1976d2;">Backend URL:</label>
            <input type="text" id="serverUrl" class="server-input" 
                   placeholder="https://your-oracle-server.com/api" 
                   value="http://localhost:5000">
            <button class="btn btn-secondary" onclick="testConnection()">Test pripojenia</button>
            <div class="server-status status-disconnected" id="serverStatus">
                Nepripojené
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message system">
                <div class="message-content">
                    Nastavte URL vašeho Oracle backend servera a otestujte pripojenie.
                </div>
            </div>
        </div>
        
        <div class="status" id="status">Nastavte backend server</div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="message-input" id="messageInput" 
                       placeholder="Napíšte správu..." 
                       onkeypress="handleKeyPress(event)"
                       disabled>
                <button class="btn btn-primary" onclick="sendMessage()" disabled id="sendBtn">Odoslať</button>
                <button class="btn-record" id="recordBtn" onclick="toggleRecording()" title="Nahrávanie hlasu" disabled>
                    🎤
                </button>
            </div>
            <div class="controls">
                <button class="btn btn-secondary" onclick="clearChat()">Vymazať chat</button>
                <button class="btn btn-secondary" onclick="loadHistory()" disabled id="historyBtn">Načítať históriu</button>
                <button class="btn btn-secondary" onclick="showSettings()">Nastavenia</button>
            </div>
        </div>
    </div>

    <script>
        // Globálne premenné
        let serverUrl = '';
        let isConnected = false;
        let isRecording = false;
        let mediaRecorder;
        let audioChunks = [];

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            const savedUrl = localStorage.getItem('serverUrl');
            if (savedUrl) {
                document.getElementById('serverUrl').value = savedUrl;
            }
            
            // Auto-test pripojenia ak je URL uložené
            if (savedUrl) {
                setTimeout(testConnection, 1000);
            }
        });

        // Test pripojenia na backend
        async function testConnection() {
            const urlInput = document.getElementById('serverUrl');
            const statusEl = document.getElementById('serverStatus');
            const testUrl = urlInput.value.trim();
            
            if (!testUrl) {
                showError('Zadajte URL backend servera');
                return;
            }
            
            // Aktualizácia statusu
            statusEl.className = 'server-status status-connecting';
            statusEl.textContent = 'Pripájam...';
            setStatus('Testujem pripojenie...');
            
            try {
                // Test základného pripojenia
                const response = await fetch(`${testUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    timeout: 5000
                });
                
                if (response.ok) {
                    serverUrl = testUrl;
                    isConnected = true;
                    
                    // Uloženie URL
                    localStorage.setItem('serverUrl', testUrl);
                    
                    // Aktualizácia UI
                    statusEl.className = 'server-status status-connected';
                    statusEl.textContent = 'Pripojené ✓';
                    setStatus('Pripravený na komunikáciu');
                    
                    // Povolenie ovládacích prvkov
                    enableControls(true);
                    
                    // Uvítacia správa
                    addMessage('system', 'Úspešne pripojené na backend server! Môžete začať chatovať.');
                    
                } else {
                    throw new Error(`Server odpovedal s kódom: ${response.status}`);
                }
                
            } catch (error) {
                console.error('Chyba pripojenia:', error);
                
                isConnected = false;
                statusEl.className = 'server-status status-disconnected';
                statusEl.textContent = 'Chyba pripojenia ✗';
                setStatus('Chyba pripojenia');
                
                enableControls(false);
                
                showError(`Nepodarilo sa pripojiť na server: ${error.message}`);
            }
        }

        // Povolenie/zakázanie ovládacích prvkov
        function enableControls(enabled) {
            document.getElementById('messageInput').disabled = !enabled;
            document.getElementById('sendBtn').disabled = !enabled;
            document.getElementById('recordBtn').disabled = !enabled;
            document.getElementById('historyBtn').disabled = !enabled;
            
            if (enabled) {
                document.getElementById('messageInput').focus();
            }
        }

        // Odoslanie správy
        async function sendMessage() {
            if (!isConnected) {
                showError('Nie ste pripojený na backend server');
                return;
            }
            
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Pridáme správu používateľa
            addMessage('user', message);
            input.value = '';
            
            // Zobrazíme loading
            setStatus('Spracovávam odpoveď...');
            
            try {
                // Pošleme na backend server
                const response = await fetch(`${serverUrl}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                } else {
                    addMessage('assistant', data.response);
                }
                
                setStatus('Pripravený na komunikáciu');
                
            } catch (error) {
                console.error('Chyba API:', error);
                showError(`Chyba pri komunikácii so serverom: ${error.message}`);
                setStatus('Chyba komunikácie');
                
                // Možno sa server odpojil
                if (error.message.includes('fetch')) {
                    isConnected = false;
                    enableControls(false);
                    document.getElementById('serverStatus').className = 'server-status status-disconnected';
                    document.getElementById('serverStatus').textContent = 'Odpojené ✗';
                }
            }
        }

        // Pridanie správy do chatu
        function addMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            if (role === 'assistant' && isConnected) {
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'message-actions';
                actionsDiv.innerHTML = `
                    <button class="btn-speak" onclick="speakText('${content.replace(/'/g, "\\'")}')">
                        🔊 Prehrať
                    </button>
                `;
                contentDiv.appendChild(actionsDiv);
            }
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Text-to-speech
        async function speakText(text) {
            if (!isConnected) {
                showError('Nie ste pripojený na backend server');
                return;
            }
            
            setStatus('Generujem hlas...');
            
            try {
                const response = await fetch(`${serverUrl}/api/speak`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const audio = new Audio();
                    audio.src = URL.createObjectURL(blob);
                    audio.play();
                    setStatus('Prehrávam hlas...');
                    
                    audio.onended = () => {
                        setStatus('Pripravený na komunikáciu');
                    };
                } else {
                    throw new Error('Chyba pri generovaní hlasu');
                }
            } catch (error) {
                console.error('TTS chyba:', error);
                showError('Chyba pri prehrávaní hlasu');
                setStatus('Pripravený na komunikáciu');
            }
        }

        // Nahrávanie hlasu (placeholder)
        function toggleRecording() {
            if (!isConnected) {
                showError('Nie ste pripojený na backend server');
                return;
            }
            
            const recordBtn = document.getElementById('recordBtn');
            
            if (!isRecording) {
                startRecording();
                recordBtn.classList.add('recording');
                recordBtn.innerHTML = '⏹️';
                setStatus('Nahrávam...');
            } else {
                stopRecording();
                recordBtn.classList.remove('recording');
                recordBtn.innerHTML = '🎤';
                setStatus('Spracovávam nahrávku...');
            }
            
            isRecording = !isRecording;
        }

        function startRecording() {
            // Placeholder pre speech-to-text
            console.log('Spúšťam nahrávanie...');
        }

        function stopRecording() {
            // Placeholder pre speech-to-text
            console.log('Ukončujem nahrávanie...');
            setTimeout(() => {
                setStatus('Pripravený na komunikáciu');
                showError('Rozpoznávanie reči nie je zatiaľ implementované. Použite textový vstup.');
            }, 1000);
        }

        // Načítanie histórie
        async function loadHistory() {
            if (!isConnected) {
                showError('Nie ste pripojený na backend server');
                return;
            }
            
            try {
                const response = await fetch(`${serverUrl}/api/history`);
                const data = await response.json();
                
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';
                
                data.history.forEach(msg => {
                    addMessage(msg.role, msg.content);
                });
                
                setStatus('História načítaná');
            } catch (error) {
                showError('Chyba pri načítavaní histórie');
            }
        }

        // Vymazanie chatu
        async function clearChat() {
            if (isConnected) {
                try {
                    await fetch(`${serverUrl}/api/clear`);
                } catch (error) {
                    console.warn('Chyba pri mazaní histórie na serveri:', error);
                }
            }
            
            document.getElementById('chatMessages').innerHTML = `
                <div class="message system">
                    <div class="message-content">
                        Chat bol vymazaný. ${isConnected ? 'Ako vám môžem pomôcť?' : 'Pripojte sa na backend server.'}
                    </div>
                </div>
            `;
            setStatus(isConnected ? 'Chat vymazaný' : 'Nastavte backend server');
        }

        // Nastavenia
        function showSettings() {
            const currentUrl = document.getElementById('serverUrl').value;
            const newUrl = prompt('Zadajte URL backend servera:', currentUrl);
            
            if (newUrl && newUrl !== currentUrl) {
                document.getElementById('serverUrl').value = newUrl;
                isConnected = false;
                enableControls(false);
                document.getElementById('serverStatus').className = 'server-status status-disconnected';
                document.getElementById('serverStatus').textContent = 'Nepripojené';
                
                addMessage('system', 'URL servera zmenené. Otestujte nové pripojenie.');
            }
        }

        // Pomocné funkcie
        function handleKeyPress(event) {
            if (event.key === 'Enter' && isConnected) {
                sendMessage();
            }
        }

        function setStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function showError(message) {
            const messagesContainer = document.getElementById('chatMessages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'message system';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.style.background = '#f8d7da';
            contentDiv.style.color = '#721c24';
            contentDiv.style.border = '1px solid #f5c6cb';
            contentDiv.textContent = '❌ ' + message;
            
            errorDiv.appendChild(contentDiv);
            messagesContainer.appendChild(errorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    </script>
</body>
</html>
