# 🎤 Slovenský hlasový chat

Moderná webová aplikácia pre hlasovú komunikáciu v slovenčine s využitím OpenAI GPT-3.5 a Piper TTS.

## ✨ Funkcie

- 💬 **Textový chat** s OpenAI GPT-3.5 Turbo (najlacnejš<PERSON> model)
- 🔊 **Text-to-Speech** pomocou Piper TTS so slovenským hlasom
- 🎤 **Rozpoznávanie reči** (pripravené na implementáciu)
- 📱 **Responzívny dizajn** pre mobily a desktop
- 💾 **História konverzácie** s možnosťou vymazania
- 🌐 **Offline TTS** - Piper beží lokálne

## 🚀 Rýchle spustenie

### 1. Inštalácia závislostí

```bash
# Vytvorenie virtuálneho prostredia
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# alebo
venv\Scripts\activate     # Windows

# Inštalácia balíčkov
pip install -r requirements.txt

# Inštalácia Piper TTS (ak nie je nain<PERSON>)
pip install piper-tts
```

### 2. Konfigurácia API kľúčov

Súbor `.env` už obsahuje vaše API kľúče:
```
OPENAI_API_KEY=***************************************************
OPENAI_ORGANIZATION=org-BjM6etktd56NAYchMAnPjVMx
```

### 3. Stiahnutie slovenského hlasového modelu

Modely sa sťahujú automaticky, ale môžete ich stiahnuť manuálne:

```bash
mkdir -p models
cd models

# Hlavný model (60 MB)
curl -L -o sk_SK-lili-medium.onnx https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx

# Konfiguračný súbor
curl -L -o sk_SK-lili-medium.onnx.json https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx.json

cd ..
```

### 4. Spustenie aplikácie

```bash
python app.py
```

Aplikácia bude dostupná na: **http://localhost:5000**

## 📁 Štruktúra projektu

```
voice_chat_app/
├── app.py                 # Hlavný Flask server
├── templates/
│   └── index.html        # Frontend aplikácia
├── models/               # Piper TTS modely
│   ├── sk_SK-lili-medium.onnx
│   └── sk_SK-lili-medium.onnx.json
├── requirements.txt      # Python závislosti
├── .env                 # API kľúče (NEZDIEĽAJTE!)
└── README.md           # Dokumentácia
```

## 🔧 API Endpointy

- `GET /` - Hlavná stránka
- `POST /api/chat` - Odoslanie správy do chatu
- `POST /api/speak` - Generovanie hlasu z textu
- `GET /api/history` - Získanie histórie konverzácie
- `GET /api/clear` - Vymazanie histórie

## 💰 Náklady

**OpenAI GPT-3.5 Turbo:**
- ~$0.0015 za 1K tokenov (vstup)
- ~$0.002 za 1K tokenov (výstup)
- Priemerná správa: ~$0.001-0.005

**Piper TTS:**
- Úplne zadarmo (beží lokálne)
- Žiadne API poplatky

## 🌐 Nasadenie na Cloudflare

### Príprava pre Cloudflare Pages

1. **Vytvorte `package.json`:**
```json
{
  "name": "slovak-voice-chat",
  "version": "1.0.0",
  "scripts": {
    "build": "echo 'No build needed'",
    "start": "python app.py"
  }
}
```

2. **Vytvorte `_redirects` súbor:**
```
/api/* /api/:splat 200
/* /index.html 200
```

3. **Upravte `app.py` pre produkciu:**
```python
if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
```

### Nasadenie

1. Nahrajte projekt na GitHub
2. Pripojte GitHub repo k Cloudflare Pages
3. Nastavte environment premenné v Cloudflare:
   - `OPENAI_API_KEY`
   - `OPENAI_ORGANIZATION`
4. Deploy!

## 🔒 Bezpečnosť

- ⚠️ **NIKDY** nezdieľajte `.env` súbor
- 🔐 API kľúče držte v tajnosti
- 🛡️ Pre produkciu použite HTTPS
- 📊 Monitorujte využitie OpenAI API

## 🎯 Budúce vylepšenia

- [ ] **Speech-to-Text** integrácia
- [ ] **Viacjazyčná podpora**
- [ ] **Hlasové profily** (rôzne hlasy)
- [ ] **Offline AI model** (napr. Ollama)
- [ ] **Mobilná aplikácia**
- [ ] **WebRTC** pre real-time komunikáciu

## 🐛 Riešenie problémov

### Piper TTS nefunguje
```bash
# Skontrolujte inštaláciu
pip install piper-tts --upgrade

# Skontrolujte cestu k modelu
ls -la models/
```

### OpenAI API chyby
```bash
# Skontrolujte API kľúč
echo $OPENAI_API_KEY

# Skontrolujte kredit na OpenAI účte
```

### Port už používaný
```bash
# Zmena portu v app.py
app.run(host='0.0.0.0', port=5001)
```

## 📞 Podpora

Pre otázky a problémy vytvorte issue alebo kontaktujte vývojára.

---

**Vyvinuté s ❤️ pre slovenskú komunitu**
