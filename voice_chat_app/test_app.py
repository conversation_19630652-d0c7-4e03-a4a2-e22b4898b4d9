#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script pre slovenský hlasový chat
"""

import requests
import json
import os
from dotenv import load_dotenv

# Načítanie environment premenných
load_dotenv()

def test_chat_api():
    """Test chat API"""
    url = "http://localhost:5000/api/chat"
    data = {
        "message": "Aho<PERSON>, ako sa máš?"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat API funguje!")
            print(f"Odpoveď: {result.get('response', 'Žiadna odpoveď')}")
            return True
        else:
            print(f"❌ Chat API chyba: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ Chyba pri testovaní Chat API: {e}")
        return False

def test_speak_api():
    """Test speak API"""
    url = "http://localhost:5000/api/speak"
    data = {
        "text": "Toto je test slovenského hlasu."
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            print("✅ Speak API funguje!")
            print(f"Audio súbor veľkosť: {len(response.content)} bajtov")
            
            # Uložíme testovací audio súbor
            with open("test_audio.wav", "wb") as f:
                f.write(response.content)
            print("💾 Audio súbor uložený ako test_audio.wav")
            return True
        else:
            print(f"❌ Speak API chyba: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ Chyba pri testovaní Speak API: {e}")
        return False

def test_history_api():
    """Test history API"""
    url = "http://localhost:5000/api/history"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            print("✅ History API funguje!")
            print(f"Počet správ v histórii: {len(result.get('history', []))}")
            return True
        else:
            print(f"❌ History API chyba: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chyba pri testovaní History API: {e}")
        return False

def main():
    print("🧪 Testovanie slovenského hlasového chatu")
    print("=" * 50)
    
    # Kontrola environment premenných
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY nie je nastavený!")
        return
    else:
        print(f"✅ OpenAI API kľúč: {api_key[:10]}...")
    
    print("\n🔄 Spúšťam testy...")
    
    # Test jednotlivých API
    tests = [
        ("Chat API", test_chat_api),
        ("Speak API", test_speak_api),
        ("History API", test_history_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Testovanie {test_name}...")
        result = test_func()
        results.append((test_name, result))
    
    # Súhrn výsledkov
    print("\n" + "=" * 50)
    print("📊 SÚHRN TESTOV:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PREŠIEL" if result else "❌ NEPREŠIEL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Výsledok: {passed}/{len(tests)} testov prešlo")
    
    if passed == len(tests):
        print("🎉 Všetky testy prešli! Aplikácia je pripravená.")
    else:
        print("⚠️  Niektoré testy neprešli. Skontrolujte konfiguráciu.")

if __name__ == "__main__":
    main()
