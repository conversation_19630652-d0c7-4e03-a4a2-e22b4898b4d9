<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Slovenský hlasový chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .chat-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .message-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
        }

        .btn-speak {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn-speak:hover {
            background: #218838;
        }

        .btn-speak:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s;
        }

        .message-input:focus {
            border-color: #007bff;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-record {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-record:hover {
            background: #c82333;
        }

        .btn-record.recording {
            background: #28a745;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .status {
            text-align: center;
            padding: 10px;
            font-size: 12px;
            color: #6c757d;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        @media (max-width: 600px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }
            
            .input-group {
                flex-wrap: wrap;
            }
            
            .message-input {
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🎤 Slovenský hlasový chat
            <div style="font-size: 14px; font-weight: normal; margin-top: 5px;">
                Powered by OpenAI GPT-3.5 + Piper TTS
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    Ahoj! Som váš slovenský hlasový asistent. Môžete so mnou písať alebo hovoriť. Ako vám môžem pomôcť?
                    <div class="message-actions">
                        <button class="btn-speak" onclick="speakText('Ahoj! Som váš slovenský hlasový asistent. Môžete so mnou písať alebo hovoriť. Ako vám môžem pomôcť?')">
                            🔊 Prehrať
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status" id="status">Pripravený na komunikáciu</div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="message-input" id="messageInput" 
                       placeholder="Napíšte správu..." 
                       onkeypress="handleKeyPress(event)">
                <button class="btn btn-primary" onclick="sendMessage()">Odoslať</button>
                <button class="btn-record" id="recordBtn" onclick="toggleRecording()" title="Nahrávanie hlasu">
                    🎤
                </button>
            </div>
            <div class="controls">
                <button class="btn btn-secondary" onclick="clearChat()">Vymazať chat</button>
                <button class="btn btn-secondary" onclick="loadHistory()">Načítať históriu</button>
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let mediaRecorder;
        let audioChunks = [];

        // Odoslanie správy
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Pridáme správu používateľa
            addMessage('user', message);
            input.value = '';
            
            // Zobrazíme loading
            setStatus('Spracovávam odpoveď...');
            
            // Pošleme na server
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                } else {
                    addMessage('assistant', data.response);
                }
                setStatus('Pripravený na komunikáciu');
            })
            .catch(error => {
                showError('Chyba pri komunikácii so serverom');
                setStatus('Pripravený na komunikáciu');
            });
        }

        // Pridanie správy do chatu
        function addMessage(role, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            if (role === 'assistant') {
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'message-actions';
                actionsDiv.innerHTML = `
                    <button class="btn-speak" onclick="speakText('${content.replace(/'/g, "\\'")}')">
                        🔊 Prehrať
                    </button>
                `;
                contentDiv.appendChild(actionsDiv);
            }
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Text-to-speech
        function speakText(text) {
            setStatus('Generujem hlas...');
            
            fetch('/api/speak', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text })
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Chyba pri generovaní hlasu');
                }
            })
            .then(blob => {
                const audio = new Audio();
                audio.src = URL.createObjectURL(blob);
                audio.play();
                setStatus('Prehrávam hlas...');
                
                audio.onended = () => {
                    setStatus('Pripravený na komunikáciu');
                };
            })
            .catch(error => {
                showError('Chyba pri prehrávaní hlasu');
                setStatus('Pripravený na komunikáciu');
            });
        }

        // Nahrávanie hlasu (placeholder - vyžaduje implementáciu speech-to-text)
        function toggleRecording() {
            const recordBtn = document.getElementById('recordBtn');
            
            if (!isRecording) {
                startRecording();
                recordBtn.classList.add('recording');
                recordBtn.innerHTML = '⏹️';
                setStatus('Nahrávam...');
            } else {
                stopRecording();
                recordBtn.classList.remove('recording');
                recordBtn.innerHTML = '🎤';
                setStatus('Spracovávam nahrávku...');
            }
            
            isRecording = !isRecording;
        }

        function startRecording() {
            // Placeholder pre speech-to-text
            // V reálnej implementácii by sme použili Web Speech API alebo iný service
            console.log('Spúšťam nahrávanie...');
        }

        function stopRecording() {
            // Placeholder pre speech-to-text
            console.log('Ukončujem nahrávanie...');
            setTimeout(() => {
                setStatus('Pripravený na komunikáciu');
                showError('Rozpoznávanie reči nie je zatiaľ implementované. Použite textový vstup.');
            }, 1000);
        }

        // Pomocné funkcie
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function setStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function showError(message) {
            const messagesContainer = document.getElementById('chatMessages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = '❌ ' + message;
            messagesContainer.appendChild(errorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function clearChat() {
            fetch('/api/clear')
            .then(response => response.json())
            .then(data => {
                document.getElementById('chatMessages').innerHTML = `
                    <div class="message assistant">
                        <div class="message-content">
                            Chat bol vymazaný. Ako vám môžem pomôcť?
                            <div class="message-actions">
                                <button class="btn-speak" onclick="speakText('Chat bol vymazaný. Ako vám môžem pomôcť?')">
                                    🔊 Prehrať
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                setStatus('Chat vymazaný');
            })
            .catch(error => {
                showError('Chyba pri mazaní chatu');
            });
        }

        function loadHistory() {
            fetch('/api/history')
            .then(response => response.json())
            .then(data => {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';
                
                data.history.forEach(msg => {
                    addMessage(msg.role, msg.content);
                });
                
                setStatus('História načítaná');
            })
            .catch(error => {
                showError('Chyba pri načítavaní histórie');
            });
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>
