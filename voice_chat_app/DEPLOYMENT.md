# 🚀 Nasadenie slovenského hlasového chatu

## 📋 Prehľad možností nasadenia

### 1. 🌐 Cloudflare Pages (Odporúčané)
- **Výhody:** <PERSON><PERSON>rm<PERSON>, r<PERSON><PERSON>le, globálne CDN
- **Nevýhody:** Obmedzenia pre Python aplikácie

### 2. 🐳 <PERSON><PERSON> kont<PERSON>
- **Výhody:** Portabilita, jednoduch<PERSON> správa
- **Nevýhody:** Vyžaduje Docker hosting

### 3. 🖥️ Lokálne spustenie
- **Výhody:** Plná kontrola, žiadne obmedzenia
- **Nevýhody:** Vyžaduje vlastný server

## 🌐 Nasadenie na Cloudflare Pages

### Krok 1: Príprava GitHub repozitára

```bash
# Inicializácia Git repozitára
git init
git add .
git commit -m "Initial commit: Slovak Voice Chat"

# Pridanie remote repozitára
git remote add origin https://github.com/YOUR_USERNAME/slovak-voice-chat.git
git push -u origin main
```

### Krok 2: Cloudflare Pages setup

1. **Prihlásenie do Cloudflare Dashboard**
   - Choďte na https://dash.cloudflare.com/
   - Vyberte "Pages" z bočného menu

2. **Vytvorenie nového projektu**
   - Kliknite "Create a project"
   - Vyberte "Connect to Git"
   - Pripojte váš GitHub repozitár

3. **Konfigurácia build settings**
   ```
   Framework preset: None
   Build command: pip install -r requirements.txt
   Build output directory: /
   Root directory: /
   ```

4. **Environment premenné**
   ```
   OPENAI_API_KEY=***************************************************
   OPENAI_ORGANIZATION=org-BjM6etktd56NAYchMAnPjVMx
   PYTHON_VERSION=3.11
   ```

### Krok 3: Custom domain (voliteľné)

```
# Pridanie vlastnej domény
slovak-voice-chat.yourdomain.com
```

## 🐳 Docker nasadenie

### Lokálne Docker spustenie

```bash
# Build Docker image
docker build -t slovak-voice-chat .

# Spustenie kontajnera
docker run -p 5000:5000 \
  -e OPENAI_API_KEY=*************************************************** \
  -e OPENAI_ORGANIZATION=org-BjM6etktd56NAYchMAnPjVMx \
  slovak-voice-chat
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  voice-chat:
    build: .
    ports:
      - "5000:5000"
    environment:
      - OPENAI_API_KEY=***************************************************
      - OPENAI_ORGANIZATION=org-BjM6etktd56NAYchMAnPjVMx
    volumes:
      - ./models:/app/models
```

```bash
# Spustenie
docker-compose up -d
```

## 🖥️ Lokálne spustenie

### Príprava prostredia

```bash
# Klonovanie repozitára
git clone https://github.com/YOUR_USERNAME/slovak-voice-chat.git
cd slovak-voice-chat

# Vytvorenie virtuálneho prostredia
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# alebo
venv\Scripts\activate     # Windows

# Inštalácia závislostí
pip install -r requirements.txt

# Inštalácia Piper TTS
pip install piper-tts
```

### Stiahnutie modelov

```bash
# Vytvorenie priečinka
mkdir -p models

# Stiahnutie slovenského modelu (60 MB)
curl -L -o models/sk_SK-lili-medium.onnx \
  https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx

# Konfiguračný súbor už existuje
```

### Spustenie aplikácie

```bash
# Spustenie Flask servera
python app.py

# Aplikácia bude dostupná na:
# http://localhost:5000
```

## 🔧 Produkčné nastavenia

### Nginx reverse proxy

```nginx
# /etc/nginx/sites-available/slovak-voice-chat
server {
    listen 80;
    server_name slovak-voice-chat.yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Systemd service

```ini
# /etc/systemd/system/slovak-voice-chat.service
[Unit]
Description=Slovak Voice Chat
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/slovak-voice-chat
Environment=PATH=/var/www/slovak-voice-chat/venv/bin
ExecStart=/var/www/slovak-voice-chat/venv/bin/python app.py
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
# Aktivácia služby
sudo systemctl enable slovak-voice-chat
sudo systemctl start slovak-voice-chat
```

## 📊 Monitoring a optimalizácia

### Loggovanie

```python
# Pridanie do app.py
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
```

### Performance monitoring

```bash
# Inštalácia monitoring nástrojov
pip install flask-limiter
pip install prometheus-flask-exporter
```

## 🔒 Bezpečnostné opatrenia

### Rate limiting

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route('/api/chat', methods=['POST'])
@limiter.limit("10 per minute")
def chat():
    # ...
```

### HTTPS certifikát

```bash
# Let's Encrypt certifikát
sudo certbot --nginx -d slovak-voice-chat.yourdomain.com
```

## 🎯 Odporúčania

1. **Pre začiatočníkov:** Cloudflare Pages
2. **Pre pokročilých:** Docker + VPS
3. **Pre produkciu:** Nginx + Systemd + HTTPS

## 📞 Podpora

Pre problémy s nasadením vytvorte issue na GitHub repozitári.
