{"name": "slovak-voice-chat", "version": "1.0.0", "description": "Slovenský hlasový chat s OpenAI API a Piper TTS", "main": "app.py", "scripts": {"build": "echo 'No build needed for Flask app'", "start": "python app.py", "dev": "python app.py", "install": "pip install -r requirements.txt"}, "keywords": ["slovak", "voice", "chat", "openai", "piper", "tts", "flask"], "author": "Slovak Voice Chat Team", "license": "MIT", "engines": {"python": ">=3.8"}}