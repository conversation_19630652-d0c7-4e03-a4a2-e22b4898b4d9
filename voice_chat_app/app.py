#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Slovenský hlasový chat s OpenAI API a Piper TTS
Webová aplikácia pre hlasovú komunikáciu v slovenčine
"""

import os
import tempfile
import subprocess
from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import openai
from dotenv import load_dotenv
import logging

# Načítanie environment premenných
load_dotenv()

app = Flask(__name__)
CORS(app)

# Konfigurácia logovania
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# OpenAI konfigurácia
openai.api_key = os.getenv('OPENAI_API_KEY')
openai.organization = os.getenv('OPENAI_ORGANIZATION')

# Cesta k Piper modelu
PIPER_MODEL_PATH = "models/sk_SK-lili-medium.onnx"

class VoiceChat:
    def __init__(self):
        self.conversation_history = []
        
    def add_message(self, role, content):
        """Pridá správu do histórie konverzácie"""
        self.conversation_history.append({"role": role, "content": content})
        
    def get_openai_response(self, user_message):
        """Získa odpoveď z OpenAI API"""
        try:
            # Pridáme správu používateľa
            self.add_message("user", user_message)
            
            # Systémová správa pre slovenský kontext
            system_message = {
                "role": "system", 
                "content": "Si užitočný asistent, ktorý odpovedá v slovenčine. Buď priateľský, informatívny a stručný. Odpovedaj prirodzene ako by si rozprával s priateľom."
            }
            
            # Pripravíme správy pre API
            messages = [system_message] + self.conversation_history[-10:]  # Posledných 10 správ
            
            # Volanie OpenAI API s najlacnejším modelom
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",  # Najlacnejší model
                messages=messages,
                max_tokens=150,
                temperature=0.7
            )
            
            assistant_message = response.choices[0].message.content.strip()
            self.add_message("assistant", assistant_message)
            
            return assistant_message
            
        except Exception as e:
            logger.error(f"Chyba pri volaní OpenAI API: {e}")
            return "Prepáčte, nastala chyba pri spracovaní vašej požiadavky."
    
    def text_to_speech(self, text):
        """Konvertuje text na reč pomocou Piper TTS"""
        try:
            # Skontrolujeme, či existuje Piper model
            if not os.path.exists(PIPER_MODEL_PATH):
                logger.warning(f"Piper model nenájdený: {PIPER_MODEL_PATH}")
                return None

            # Vytvoríme dočasný súbor
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

            # Spustíme Piper TTS
            process = subprocess.Popen(
                ["piper", "--model", PIPER_MODEL_PATH, "--output_file", temp_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            stdout, stderr = process.communicate(input=text)

            if process.returncode == 0:
                return temp_path
            else:
                logger.error(f"Piper TTS chyba: {stderr}")
                return None

        except Exception as e:
            logger.error(f"Chyba pri generovaní reči: {e}")
            return None

# Globálna inštancia chat-u
voice_chat = VoiceChat()

@app.route('/')
def index():
    """Hlavná stránka"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """API endpoint pre chat"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'Prázdna správa'}), 400
        
        # Získame odpoveď z OpenAI
        response = voice_chat.get_openai_response(user_message)
        
        return jsonify({
            'response': response,
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"Chyba v chat API: {e}")
        return jsonify({'error': 'Chyba servera'}), 500

@app.route('/api/speak', methods=['POST'])
def speak():
    """API endpoint pre text-to-speech"""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        
        if not text:
            return jsonify({'error': 'Prázdny text'}), 400
        
        # Generujeme audio súbor
        audio_path = voice_chat.text_to_speech(text)
        
        if audio_path:
            return send_file(
                audio_path, 
                as_attachment=True, 
                download_name='response.wav',
                mimetype='audio/wav'
            )
        else:
            return jsonify({'error': 'Chyba pri generovaní audio'}), 500
            
    except Exception as e:
        logger.error(f"Chyba v speak API: {e}")
        return jsonify({'error': 'Chyba servera'}), 500

@app.route('/api/history')
def get_history():
    """Získa históriu konverzácie"""
    return jsonify({
        'history': voice_chat.conversation_history[-20:],  # Posledných 20 správ
        'status': 'success'
    })

@app.route('/api/clear')
def clear_history():
    """Vymaže históriu konverzácie"""
    voice_chat.conversation_history = []
    return jsonify({'status': 'success', 'message': 'História vymazaná'})

if __name__ == '__main__':
    # Kontrola, či existuje Piper model
    if not os.path.exists(PIPER_MODEL_PATH):
        logger.warning(f"Piper model nenájdený na: {PIPER_MODEL_PATH}")
        logger.info("Skúste upraviť cestu PIPER_MODEL_PATH v app.py")
    
    # Kontrola OpenAI API kľúča
    if not openai.api_key:
        logger.error("OPENAI_API_KEY nie je nastavený!")
        logger.info("Vytvorte .env súbor s OPENAI_API_KEY a OPENAI_ORGANIZATION")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
