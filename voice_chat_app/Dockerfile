# Slovenský hlasový chat - Docker image
FROM python:3.11-slim

# Nastavenie pracovného adresára
WORKDIR /app

# Inštalácia systémových závislostí
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    espeak-ng \
    espeak-ng-data \
    libespeak-ng1 \
    && rm -rf /var/lib/apt/lists/*

# Kopírovanie requirements súboru
COPY requirements.txt .

# Inštalácia Python závislostí
RUN pip install --no-cache-dir -r requirements.txt

# Kopírovanie aplikácie
COPY . .

# Vytvorenie priečinka pre modely
RUN mkdir -p models

# Stiahnutie slovenského hlasového modelu (voliteľné)
# RUN curl -L -o models/sk_SK-lili-medium.onnx.json \
#     https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx.json

# Exponovanie portu
EXPOSE 5000

# Environment premenné
ENV FLASK_APP=app.py
ENV FLASK_ENV=production

# Spustenie aplikácie
CMD ["python", "app.py"]
